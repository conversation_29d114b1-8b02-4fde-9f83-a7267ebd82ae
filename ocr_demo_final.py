import os
from paddleocr import PaddleOCR, TextDetection

def main():
    # 创建输出目录
    os.makedirs("./output", exist_ok=True)

    # 图片路径
    image_path = '/Users/<USER>/Downloads/999999999999999测试图片.png'

    print("=" * 60)
    print("PaddleOCR 演示程序")
    print("=" * 60)

    # 方法1: 使用 PaddleOCR 进行完整的 OCR 识别（检测 + 识别）
    print("\n🔍 方法1: 使用 PaddleOCR 进行完整 OCR 识别")
    print("-" * 40)

    try:
        # 初始化 OCR
        ocr = PaddleOCR(
            use_textline_orientation=True,  # 使用文字方向分类
            lang='ch',                      # 中文
            ocr_version='PP-OCRv5'         # 使用 PP-OCRv5 版本
        )

        # 进行 OCR 识别
        results = ocr.predict(image_path)

        print("✅ OCR 识别完成！")
        print(f"📄 识别结果数量: {len(results)}")

        # 解析并显示结果
        for idx, result in enumerate(results):
            print(f"\n📖 第 {idx+1} 页识别结果:")


            # 显示完整的原始结果（用于调试）
            print("  🔧 完整原始结果:")
            if hasattr(result, 'print'):
                result.print()

    except Exception as e:
        print(f"❌ PaddleOCR 识别失败: {e}")

    print("\n" + "=" * 60)

    # 方法2: 使用 TextDetection 进行文字检测（仅检测，不识别）
    print("\n🎯 方法2: 使用 TextDetection 进行文字检测")
    print("-" * 40)

    try:
        # 初始化文字检测模型
        model = TextDetection(model_name="PP-OCRv5_server_det")

        # 检查图片是否存在
        if os.path.exists(image_path):
            # 进行文字检测
            output = model.predict(input=image_path, batch_size=1)

            print("✅ 文字检测完成！")
            print("📍 检测结果:")

            for res in output:
                res.print()

                # 保存结果
                res.save_to_img(save_path="./output/")
                res.save_to_json(save_path="./output/res.json")

            print("💾 结果已保存到 ./output/ 目录")
            print("   - 检测结果图片: ./output/")
            print("   - 检测结果JSON: ./output/res.json")

        else:
            print(f"❌ 图片文件不存在: {image_path}")
            print("请检查图片路径是否正确")

    except Exception as e:
        print(f"❌ TextDetection 检测失败: {e}")
        print("可能需要安装额外的依赖或检查模型配置")

    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()