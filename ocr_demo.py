import os
from paddleocr import PaddleOCR, TextDetection

# 创建输出目录
os.makedirs("./output", exist_ok=True)

# 识别图片
image_path = '/Users/<USER>/Downloads/999999999999999测试图片.png'
# 检查图片是否存在
if not os.path.exists(image_path):
    print(f"图片文件不存在: {image_path}")
    print("请检查图片路径是否正确")
    exit()

# 方法2: 使用 TextDetection (仅文字检测)
print("=== 方法2: 使用 TextDetection 进行文字检测 ===")
try:
    model = TextDetection(model_name="PP-OCRv5_server_det")

    output = model.predict(input=image_path, batch_size=1)

    print("文字检测结果:")
    for res in output:
        res.print()
        res.save_to_img(save_path="./output/res.png")
        res.save_to_json(save_path="./output/res.json")
        print("结果已保存到 ./output/ 目录")


except Exception as e:
    print(f"TextDetection 方法出错: {e}")
    print("可能需要安装额外的依赖或检查模型配置")
